@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: ===========================================
:: 若依Vue Pro - 数据库管理脚本
:: ===========================================

:: 加载配置
if exist "deploy-config.bat" (
    call deploy-config.bat
) else (
    set "MYSQL_HOST=localhost"
    set "MYSQL_PORT=3306"
    set "MYSQL_DATABASE=ruoyi-vue-pro"
    set "MYSQL_USERNAME=root"
    set "MYSQL_PASSWORD=123456"
)

:: 设置颜色
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

set "PROJECT_ROOT=%~dp0"

echo.
echo %BLUE%========================================%NC%
echo %BLUE%  若依Vue Pro 数据库管理工具%NC%
echo %BLUE%========================================%NC%
echo.

if "%1"=="init" goto :init_database
if "%1"=="backup" goto :backup_database
if "%1"=="restore" goto :restore_database
if "%1"=="reset" goto :reset_database
if "%1"=="check" goto :check_database
if "%1"=="help" goto :show_help

:show_menu
echo %YELLOW%请选择操作：%NC%
echo   1. 初始化数据库
echo   2. 备份数据库
echo   3. 恢复数据库
echo   4. 重置数据库
echo   5. 检查数据库连接
echo   6. 退出
echo.
set /p choice=请输入选项 (1-6): 

if "%choice%"=="1" goto :init_database
if "%choice%"=="2" goto :backup_database
if "%choice%"=="3" goto :restore_database
if "%choice%"=="4" goto :reset_database
if "%choice%"=="5" goto :check_database
if "%choice%"=="6" goto :end
echo %RED%无效选项，请重新选择%NC%
goto :show_menu

:init_database
echo.
echo %YELLOW%正在初始化数据库...%NC%

:: 检查MySQL连接
mysql -h%MYSQL_HOST% -P%MYSQL_PORT% -u%MYSQL_USERNAME% -p%MYSQL_PASSWORD% -e "SELECT 1;" >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ 无法连接到MySQL数据库%NC%
    echo %YELLOW%请检查数据库配置：%NC%
    echo   主机: %MYSQL_HOST%:%MYSQL_PORT%
    echo   用户: %MYSQL_USERNAME%
    goto :end
)

:: 创建数据库
echo %BLUE%正在创建数据库...%NC%
mysql -h%MYSQL_HOST% -P%MYSQL_PORT% -u%MYSQL_USERNAME% -p%MYSQL_PASSWORD% -e "CREATE DATABASE IF NOT EXISTS %MYSQL_DATABASE% CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
if errorlevel 1 (
    echo %RED%❌ 数据库创建失败%NC%
    goto :end
)

:: 导入主数据库结构
echo %BLUE%正在导入数据库结构...%NC%
mysql -h%MYSQL_HOST% -P%MYSQL_PORT% -u%MYSQL_USERNAME% -p%MYSQL_PASSWORD% %MYSQL_DATABASE% < "%PROJECT_ROOT%sql\mysql\ruoyi-vue-pro.sql"
if errorlevel 1 (
    echo %RED%❌ 数据库结构导入失败%NC%
    goto :end
)

:: 导入Quartz表
echo %BLUE%正在导入Quartz任务调度表...%NC%
mysql -h%MYSQL_HOST% -P%MYSQL_PORT% -u%MYSQL_USERNAME% -p%MYSQL_PASSWORD% %MYSQL_DATABASE% < "%PROJECT_ROOT%sql\mysql\quartz.sql"
if errorlevel 1 (
    echo %YELLOW%⚠️  Quartz表导入失败，但不影响基本功能%NC%
)

echo %GREEN%✅ 数据库初始化完成%NC%
goto :show_menu

:backup_database
echo.
echo %YELLOW%正在备份数据库...%NC%

:: 创建备份目录
if not exist "backup" mkdir backup

:: 生成备份文件名
for /f "tokens=1-4 delims=/ " %%a in ('date /t') do set backup_date=%%d%%b%%c
for /f "tokens=1-2 delims=: " %%a in ('time /t') do set backup_time=%%a%%b
set "backup_file=backup\%MYSQL_DATABASE%_%backup_date%_%backup_time%.sql"

:: 执行备份
mysqldump -h%MYSQL_HOST% -P%MYSQL_PORT% -u%MYSQL_USERNAME% -p%MYSQL_PASSWORD% --single-transaction --routines --triggers %MYSQL_DATABASE% > "%backup_file%"
if errorlevel 1 (
    echo %RED%❌ 数据库备份失败%NC%
    goto :end
)

echo %GREEN%✅ 数据库备份完成%NC%
echo %BLUE%备份文件: %backup_file%%NC%
goto :show_menu

:restore_database
echo.
echo %YELLOW%数据库恢复功能%NC%
echo.

:: 列出备份文件
if not exist "backup\*.sql" (
    echo %RED%❌ 未找到备份文件%NC%
    goto :show_menu
)

echo %BLUE%可用的备份文件：%NC%
set /a count=0
for %%f in (backup\*.sql) do (
    set /a count+=1
    echo   !count!. %%f
    set "backup_!count!=%%f"
)

if %count%==0 (
    echo %RED%❌ 未找到备份文件%NC%
    goto :show_menu
)

echo.
set /p restore_choice=请选择要恢复的备份文件 (1-%count%): 

if !restore_choice! lss 1 goto :restore_database
if !restore_choice! gtr %count% goto :restore_database

set "restore_file=!backup_%restore_choice%!"

echo.
echo %RED%⚠️  警告：此操作将覆盖当前数据库的所有数据！%NC%
set /p confirm=确认恢复数据库？(y/n): 
if /i not "%confirm%"=="y" goto :show_menu

echo %YELLOW%正在恢复数据库...%NC%
mysql -h%MYSQL_HOST% -P%MYSQL_PORT% -u%MYSQL_USERNAME% -p%MYSQL_PASSWORD% %MYSQL_DATABASE% < "%restore_file%"
if errorlevel 1 (
    echo %RED%❌ 数据库恢复失败%NC%
    goto :end
)

echo %GREEN%✅ 数据库恢复完成%NC%
goto :show_menu

:reset_database
echo.
echo %RED%⚠️  警告：此操作将删除并重新创建数据库！%NC%
echo %RED%所有数据将丢失！%NC%
echo.
set /p confirm=确认重置数据库？(y/n): 
if /i not "%confirm%"=="y" goto :show_menu

echo %YELLOW%正在重置数据库...%NC%

:: 删除数据库
mysql -h%MYSQL_HOST% -P%MYSQL_PORT% -u%MYSQL_USERNAME% -p%MYSQL_PASSWORD% -e "DROP DATABASE IF EXISTS %MYSQL_DATABASE%;"

:: 重新初始化
goto :init_database

:check_database
echo.
echo %BLUE%检查数据库连接...%NC%

mysql -h%MYSQL_HOST% -P%MYSQL_PORT% -u%MYSQL_USERNAME% -p%MYSQL_PASSWORD% -e "SELECT VERSION() as MySQL版本, NOW() as 当前时间;" 2>nul
if errorlevel 1 (
    echo %RED%❌ 数据库连接失败%NC%
    echo %YELLOW%请检查以下配置：%NC%
    echo   主机: %MYSQL_HOST%
    echo   端口: %MYSQL_PORT%
    echo   用户名: %MYSQL_USERNAME%
    echo   密码: %MYSQL_PASSWORD%
) else (
    echo %GREEN%✅ 数据库连接正常%NC%
    
    :: 检查数据库是否存在
    mysql -h%MYSQL_HOST% -P%MYSQL_PORT% -u%MYSQL_USERNAME% -p%MYSQL_PASSWORD% -e "USE %MYSQL_DATABASE%; SELECT COUNT(*) as 表数量 FROM information_schema.tables WHERE table_schema='%MYSQL_DATABASE%';" 2>nul
    if errorlevel 1 (
        echo %YELLOW%⚠️  数据库 %MYSQL_DATABASE% 不存在%NC%
    ) else (
        echo %GREEN%✅ 数据库 %MYSQL_DATABASE% 存在%NC%
    )
)

goto :show_menu

:show_help
echo.
echo %BLUE%数据库管理工具使用说明%NC%
echo.
echo %YELLOW%命令行用法：%NC%
echo   %0 init     - 初始化数据库
echo   %0 backup   - 备份数据库
echo   %0 restore  - 恢复数据库
echo   %0 reset    - 重置数据库
echo   %0 check    - 检查数据库连接
echo   %0 help     - 显示帮助信息
echo.
echo %YELLOW%交互模式：%NC%
echo   直接运行 %0 进入交互菜单
echo.
goto :end

:end
if "%1"=="" (
    echo.
    pause
)
