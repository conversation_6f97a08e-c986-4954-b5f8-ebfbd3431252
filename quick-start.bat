@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: ===========================================
:: 若依Vue Pro + AI模块 - 快速启动脚本
:: 适用于已完成初始部署的环境
:: ===========================================

:: 加载配置
if exist "deploy-config.bat" (
    call deploy-config.bat
) else (
    echo 配置文件不存在，使用默认配置
    set "BACKEND_PORT=48080"
    set "FRONTEND_PORT=3000"
)

:: 设置颜色
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

set "PROJECT_ROOT=%~dp0"
set "BACKEND_DIR=%PROJECT_ROOT%"
set "FRONTEND_DIR=%PROJECT_ROOT%yudao-ui-admin-vue3"

echo.
echo %BLUE%========================================%NC%
echo %BLUE%  若依Vue Pro + AI模块 快速启动%NC%
echo %BLUE%========================================%NC%
echo.

:: 检查后端JAR文件
if not exist "%BACKEND_DIR%yudao-server\target\yudao-server.jar" (
    echo %RED%❌ 后端JAR文件不存在%NC%
    echo %YELLOW%请先运行 deploy-demo.bat 进行完整部署%NC%
    pause
    exit /b 1
)

:: 检查前端依赖
if not exist "%FRONTEND_DIR%\node_modules" (
    echo %YELLOW%⚠️  前端依赖不存在，正在安装...%NC%
    cd /d "%FRONTEND_DIR%"
    call pnpm install
    if errorlevel 1 (
        echo %RED%❌ 前端依赖安装失败%NC%
        pause
        exit /b 1
    )
)

:: 检查端口占用
echo %BLUE%检查端口占用情况...%NC%
netstat -an | findstr ":%BACKEND_PORT%" >nul 2>&1
if not errorlevel 1 (
    echo %YELLOW%⚠️  后端端口 %BACKEND_PORT% 已被占用%NC%
    echo %YELLOW%是否要停止现有服务？(y/n)%NC%
    set /p kill_backend=
    if /i "!kill_backend!"=="y" (
        for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":%BACKEND_PORT%"') do (
            taskkill /f /pid %%a >nul 2>&1
        )
        echo %GREEN%✅ 已停止占用端口的进程%NC%
    )
)

netstat -an | findstr ":%FRONTEND_PORT%" >nul 2>&1
if not errorlevel 1 (
    echo %YELLOW%⚠️  前端端口 %FRONTEND_PORT% 已被占用%NC%
    echo %YELLOW%是否要停止现有服务？(y/n)%NC%
    set /p kill_frontend=
    if /i "!kill_frontend!"=="y" (
        for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":%FRONTEND_PORT%"') do (
            taskkill /f /pid %%a >nul 2>&1
        )
        echo %GREEN%✅ 已停止占用端口的进程%NC%
    )
)

:: 启动后端服务
echo.
echo %YELLOW%正在启动后端服务...%NC%
cd /d "%BACKEND_DIR%"
start "若依后端服务" cmd /k "java %JAVA_OPTS% -jar yudao-server\target\yudao-server.jar --spring.profiles.active=%SPRING_PROFILES_ACTIVE% --server.port=%BACKEND_PORT%"

:: 等待后端启动
echo %BLUE%等待后端服务启动...%NC%
set /a count=0
:wait_backend
timeout /t 2 /nobreak >nul
set /a count+=1
netstat -an | findstr ":%BACKEND_PORT%" >nul 2>&1
if errorlevel 1 (
    if !count! lss 30 (
        echo %BLUE%等待中... (!count!/30)%NC%
        goto :wait_backend
    ) else (
        echo %RED%❌ 后端服务启动超时%NC%
        pause
        exit /b 1
    )
)
echo %GREEN%✅ 后端服务启动成功%NC%

:: 启动前端服务
echo.
echo %YELLOW%正在启动前端服务...%NC%
cd /d "%FRONTEND_DIR%"
start "若依前端服务" cmd /k "pnpm dev"

:: 等待前端启动
echo %BLUE%等待前端服务启动...%NC%
timeout /t 5 /nobreak >nul

echo.
echo %GREEN%========================================%NC%
echo %GREEN%  服务启动完成！%NC%
echo %GREEN%========================================%NC%
echo.
echo %BLUE%访问地址：%NC%
echo   🌐 前端管理后台: http://localhost:%FRONTEND_PORT%
echo   🔧 后端API接口: http://localhost:%BACKEND_PORT%
echo   📚 API文档: http://localhost:%BACKEND_PORT%/doc.html
echo.
echo %BLUE%默认账号：%NC%
echo   👤 用户名: admin
echo   🔑 密码: admin123
echo.
echo %YELLOW%快捷操作：%NC%
echo   停止服务: quick-start.bat stop
echo   查看状态: quick-start.bat status
echo   重启服务: quick-start.bat restart
echo.

:: 处理命令行参数
if "%1"=="stop" goto :stop_services
if "%1"=="status" goto :check_status  
if "%1"=="restart" goto :restart_services

goto :end

:stop_services
echo %YELLOW%正在停止所有服务...%NC%
taskkill /f /im java.exe /fi "WINDOWTITLE eq 若依后端服务*" >nul 2>&1
taskkill /f /im node.exe /fi "WINDOWTITLE eq 若依前端服务*" >nul 2>&1
echo %GREEN%✅ 所有服务已停止%NC%
goto :end

:check_status
echo %BLUE%服务状态检查：%NC%
echo.
netstat -an | findstr ":%BACKEND_PORT%" >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ 后端服务未运行%NC%
) else (
    echo %GREEN%✅ 后端服务正在运行 (端口 %BACKEND_PORT%)%NC%
)

netstat -an | findstr ":%FRONTEND_PORT%" >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ 前端服务未运行%NC%
) else (
    echo %GREEN%✅ 前端服务正在运行 (端口 %FRONTEND_PORT%)%NC%
)
goto :end

:restart_services
echo %YELLOW%正在重启服务...%NC%
call :stop_services
timeout /t 3 /nobreak >nul
goto :start_services

:end
if "%1"=="" (
    echo %BLUE%按任意键退出...%NC%
    pause >nul
)
