@echo off
:: ===========================================
:: 若依Vue Pro + AI模块 - 部署配置文件
:: 请根据您的环境修改以下配置参数
:: ===========================================

:: 数据库配置
set "MYSQL_HOST=localhost"
set "MYSQL_PORT=3306"
set "MYSQL_DATABASE=ruoyi-vue-pro"
set "MYSQL_USERNAME=root"
set "MYSQL_PASSWORD=123456"

:: Redis配置
set "REDIS_HOST=localhost"
set "REDIS_PORT=6379"
set "REDIS_PASSWORD="

:: 服务端口配置
set "BACKEND_PORT=48080"
set "FRONTEND_PORT=3000"

:: JVM配置
set "JAVA_OPTS=-Xms512m -Xmx2048m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m"

:: Maven配置
set "MAVEN_OPTS=-Xmx1024m"

:: 环境配置
set "SPRING_PROFILES_ACTIVE=local"

:: AI模块配置（可选）
set "OPENAI_API_KEY="
set "OPENAI_API_BASE="

:: 文件上传路径
set "FILE_UPLOAD_PATH=%~dp0uploads"

:: 日志级别
set "LOG_LEVEL=INFO"

echo 配置文件加载完成
echo 数据库: %MYSQL_HOST%:%MYSQL_PORT%/%MYSQL_DATABASE%
echo 后端端口: %BACKEND_PORT%
echo 前端端口: %FRONTEND_PORT%
