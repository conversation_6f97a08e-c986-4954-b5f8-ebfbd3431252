# 若依Vue Pro + AI模块 - Windows演示环境部署指南

## 📋 概述

本部署包提供了若依Vue Pro项目（包含AI模块）在Windows环境下的一键部署解决方案。

## 🛠️ 环境要求

### 必需环境
- **JDK 17+** - Java开发环境
- **Maven 3.6+** - Java项目构建工具
- **Node.js 16+** - 前端运行环境
- **MySQL 5.7+** - 数据库
- **Git** - 版本控制工具

### 可选环境
- **Redis** - 缓存数据库（推荐）
- **pnpm** - 前端包管理器（自动安装）

## 🚀 快速开始

### 1. 完整部署（首次使用）

```bash
# 双击运行或在命令行执行
deploy-demo.bat
```

这个脚本会：
- ✅ 检查所有必需的环境
- ✅ 自动安装缺失的工具（如pnpm）
- ✅ 初始化MySQL数据库
- ✅ 编译后端项目
- ✅ 安装前端依赖
- ✅ 启动所有服务

### 2. 快速启动（已部署环境）

```bash
# 适用于已完成初始部署的环境
quick-start.bat
```

## 📁 脚本文件说明

| 文件名 | 功能描述 |
|--------|----------|
| `deploy-demo.bat` | 完整部署脚本，包含环境检查和初始化 |
| `quick-start.bat` | 快速启动脚本，适用于已部署环境 |
| `deploy-config.bat` | 配置文件，可自定义数据库等参数 |
| `db-manager.bat` | 数据库管理工具 |

## ⚙️ 配置说明

### 修改配置参数

编辑 `deploy-config.bat` 文件来自定义配置：

```batch
:: 数据库配置
set "MYSQL_HOST=localhost"
set "MYSQL_PORT=3306"
set "MYSQL_DATABASE=ruoyi-vue-pro"
set "MYSQL_USERNAME=root"
set "MYSQL_PASSWORD=123456"

:: 服务端口配置
set "BACKEND_PORT=48080"
set "FRONTEND_PORT=3000"
```

## 🎯 常用命令

### 部署脚本命令

```bash
# 完整部署
deploy-demo.bat

# 仅启动后端
deploy-demo.bat start-backend

# 仅启动前端
deploy-demo.bat start-frontend

# 启动所有服务
deploy-demo.bat start-all

# 停止所有服务
deploy-demo.bat stop

# 检查服务状态
deploy-demo.bat status

# 显示帮助
deploy-demo.bat help
```

### 快速启动命令

```bash
# 启动所有服务
quick-start.bat

# 停止所有服务
quick-start.bat stop

# 检查服务状态
quick-start.bat status

# 重启所有服务
quick-start.bat restart
```

### 数据库管理命令

```bash
# 交互式菜单
db-manager.bat

# 初始化数据库
db-manager.bat init

# 备份数据库
db-manager.bat backup

# 恢复数据库
db-manager.bat restore

# 重置数据库
db-manager.bat reset

# 检查数据库连接
db-manager.bat check
```

## 🌐 访问地址

部署成功后，可通过以下地址访问：

- **前端管理后台**: http://localhost:3000
- **后端API接口**: http://localhost:48080
- **API文档**: http://localhost:48080/doc.html

### 默认账号

- **用户名**: admin
- **密码**: admin123

## 🔧 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 检查端口占用
netstat -ano | findstr :48080
netstat -ano | findstr :3000

# 停止占用进程
taskkill /f /pid [进程ID]
```

#### 2. 数据库连接失败
- 检查MySQL服务是否启动
- 验证数据库连接参数
- 确认防火墙设置

#### 3. 前端依赖安装失败
```bash
# 清理缓存重新安装
cd yudao-ui-admin-vue3
pnpm clean
pnpm install
```

#### 4. 后端编译失败
```bash
# 清理并重新编译
mvn clean package -DskipTests
```

### 日志查看

- **后端日志**: 在启动的命令行窗口中查看
- **前端日志**: 在启动的命令行窗口中查看
- **数据库日志**: 检查MySQL错误日志

## 🎨 AI功能配置

### 配置AI平台API密钥

1. 登录管理后台
2. 进入 `AI大模型 -> API密钥管理`
3. 添加相应平台的API密钥：
   - OpenAI
   - 百度文心一言
   - 阿里通义千问
   - 腾讯混元
   - 等等

### AI功能模块

- **AI对话**: 智能聊天助手
- **AI绘画**: 图像生成功能
- **AI写作**: 文本生成助手
- **AI音乐**: 音乐创作功能
- **知识库**: 文档问答系统

## 📞 技术支持

如遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查命令行输出的错误信息
3. 确认环境配置是否正确
4. 查看相关日志文件

## 📝 更新日志

### v1.0 (2025-01-XX)
- ✅ 初始版本发布
- ✅ 支持Windows一键部署
- ✅ 集成AI模块功能
- ✅ 提供完整的管理脚本

---

**注意**: 本部署方案适用于开发和演示环境，生产环境请根据实际需求进行相应的安全配置和性能优化。
