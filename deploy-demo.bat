@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: ===========================================
:: 若依Vue Pro + AI模块 - Windows演示环境一键部署脚本
:: 作者：AI助手
:: 版本：v1.0
:: ===========================================

echo.
echo ========================================
echo   若依Vue Pro + AI模块 演示环境部署
echo ========================================
echo.

:: 设置颜色
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

:: 项目根目录
set "PROJECT_ROOT=%~dp0"
set "BACKEND_DIR=%PROJECT_ROOT%"
set "FRONTEND_DIR=%PROJECT_ROOT%yudao-ui-admin-vue3"

:: 配置参数
set "MYSQL_HOST=localhost"
set "MYSQL_PORT=3306"
set "MYSQL_DATABASE=ruoyi-vue-pro"
set "MYSQL_USERNAME=root"
set "MYSQL_PASSWORD=123456"
set "REDIS_HOST=localhost"
set "REDIS_PORT=6379"
set "BACKEND_PORT=48080"
set "FRONTEND_PORT=80"

echo %BLUE%正在检查部署环境...%NC%
echo.

:: 检查Java环境
echo %YELLOW%[1/8] 检查Java环境...%NC%
java -version >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ 未检测到Java环境，请先安装JDK 17或更高版本%NC%
    echo %YELLOW%下载地址：https://www.oracle.com/java/technologies/downloads/%NC%
    pause
    exit /b 1
) else (
    echo %GREEN%✅ Java环境检查通过%NC%
)

:: 检查Maven环境
echo %YELLOW%[2/8] 检查Maven环境...%NC%
mvn -version >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ 未检测到Maven环境，请先安装Maven%NC%
    echo %YELLOW%下载地址：https://maven.apache.org/download.cgi%NC%
    pause
    exit /b 1
) else (
    echo %GREEN%✅ Maven环境检查通过%NC%
)

:: 检查Node.js环境
echo %YELLOW%[3/8] 检查Node.js环境...%NC%
node -v >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ 未检测到Node.js环境，请先安装Node.js 16或更高版本%NC%
    echo %YELLOW%下载地址：https://nodejs.org/%NC%
    pause
    exit /b 1
) else (
    echo %GREEN%✅ Node.js环境检查通过%NC%
)

:: 检查pnpm
echo %YELLOW%[4/8] 检查pnpm包管理器...%NC%
pnpm -v >nul 2>&1
if errorlevel 1 (
    echo %YELLOW%⚠️  未检测到pnpm，正在安装...%NC%
    npm install -g pnpm
    if errorlevel 1 (
        echo %RED%❌ pnpm安装失败%NC%
        pause
        exit /b 1
    )
) else (
    echo %GREEN%✅ pnpm检查通过%NC%
)

:: 检查MySQL连接
echo %YELLOW%[5/8] 检查MySQL数据库连接...%NC%
mysql -h%MYSQL_HOST% -P%MYSQL_PORT% -u%MYSQL_USERNAME% -p%MYSQL_PASSWORD% -e "SELECT 1;" >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ 无法连接到MySQL数据库%NC%
    echo %YELLOW%请确保MySQL服务已启动，并检查连接参数：%NC%
    echo   主机: %MYSQL_HOST%
    echo   端口: %MYSQL_PORT%
    echo   用户名: %MYSQL_USERNAME%
    echo   密码: %MYSQL_PASSWORD%
    echo.
    echo %YELLOW%是否要修改数据库连接参数？(y/n)%NC%
    set /p modify_db=
    if /i "!modify_db!"=="y" (
        call :configure_database
    ) else (
        pause
        exit /b 1
    )
) else (
    echo %GREEN%✅ MySQL数据库连接正常%NC%
)

:: 检查Redis连接
echo %YELLOW%[6/8] 检查Redis连接...%NC%
redis-cli -h %REDIS_HOST% -p %REDIS_PORT% ping >nul 2>&1
if errorlevel 1 (
    echo %YELLOW%⚠️  Redis连接失败，将使用内存模式（不推荐生产环境）%NC%
) else (
    echo %GREEN%✅ Redis连接正常%NC%
)

:: 初始化数据库
echo %YELLOW%[7/8] 初始化数据库...%NC%
echo %BLUE%正在检查数据库是否已存在...%NC%
mysql -h%MYSQL_HOST% -P%MYSQL_PORT% -u%MYSQL_USERNAME% -p%MYSQL_PASSWORD% -e "USE %MYSQL_DATABASE%;" >nul 2>&1
if errorlevel 1 (
    echo %YELLOW%数据库不存在，正在创建...%NC%
    mysql -h%MYSQL_HOST% -P%MYSQL_PORT% -u%MYSQL_USERNAME% -p%MYSQL_PASSWORD% -e "CREATE DATABASE IF NOT EXISTS %MYSQL_DATABASE% CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    if errorlevel 1 (
        echo %RED%❌ 数据库创建失败%NC%
        pause
        exit /b 1
    )
    
    echo %BLUE%正在导入数据库结构...%NC%
    mysql -h%MYSQL_HOST% -P%MYSQL_PORT% -u%MYSQL_USERNAME% -p%MYSQL_PASSWORD% %MYSQL_DATABASE% < "%PROJECT_ROOT%sql\mysql\ruoyi-vue-pro.sql"
    if errorlevel 1 (
        echo %RED%❌ 数据库结构导入失败%NC%
        pause
        exit /b 1
    )
    
    echo %BLUE%正在导入Quartz任务调度表...%NC%
    mysql -h%MYSQL_HOST% -P%MYSQL_PORT% -u%MYSQL_USERNAME% -p%MYSQL_PASSWORD% %MYSQL_DATABASE% < "%PROJECT_ROOT%sql\mysql\quartz.sql"
    if errorlevel 1 (
        echo %YELLOW%⚠️  Quartz表导入失败，但不影响基本功能%NC%
    )
    
    echo %GREEN%✅ 数据库初始化完成%NC%
) else (
    echo %GREEN%✅ 数据库已存在，跳过初始化%NC%
)

:: 编译后端项目
echo %YELLOW%[8/8] 编译后端项目...%NC%
cd /d "%BACKEND_DIR%"
echo %BLUE%正在清理并编译项目...%NC%
call mvn clean package -DskipTests -q
if errorlevel 1 (
    echo %RED%❌ 后端项目编译失败%NC%
    pause
    exit /b 1
) else (
    echo %GREEN%✅ 后端项目编译成功%NC%
)

echo.
echo %GREEN%========================================%NC%
echo %GREEN%  环境检查和准备工作完成！%NC%
echo %GREEN%========================================%NC%
echo.

:: 询问是否立即启动服务
echo %YELLOW%是否立即启动演示环境？(y/n)%NC%
set /p start_services=
if /i "!start_services!"=="y" (
    call :start_services
) else (
    echo.
    echo %BLUE%部署准备完成！%NC%
    echo %YELLOW%手动启动命令：%NC%
    echo   后端: %0 start-backend
    echo   前端: %0 start-frontend
    echo   全部: %0 start-all
    echo.
)

goto :end

:: ===========================================
:: 函数定义区域
:: ===========================================

:configure_database
echo.
echo %YELLOW%请输入数据库连接参数：%NC%
set /p MYSQL_HOST=MySQL主机地址 [%MYSQL_HOST%]: 
set /p MYSQL_PORT=MySQL端口 [%MYSQL_PORT%]: 
set /p MYSQL_DATABASE=数据库名 [%MYSQL_DATABASE%]: 
set /p MYSQL_USERNAME=用户名 [%MYSQL_USERNAME%]: 
set /p MYSQL_PASSWORD=密码 [%MYSQL_PASSWORD%]: 
echo.
goto :eof

:start_services
echo.
echo %BLUE%正在启动演示环境...%NC%
echo.

:: 启动后端服务
echo %YELLOW%启动后端服务...%NC%
cd /d "%BACKEND_DIR%"
start "若依后端服务" cmd /k "java -jar yudao-server\target\yudao-server.jar --spring.profiles.active=local --server.port=%BACKEND_PORT%"

:: 等待后端启动
echo %BLUE%等待后端服务启动...%NC%
timeout /t 10 /nobreak >nul

:: 安装前端依赖并启动
echo %YELLOW%安装前端依赖并启动...%NC%
cd /d "%FRONTEND_DIR%"
if not exist "node_modules" (
    echo %BLUE%正在安装前端依赖...%NC%
    call pnpm install
    if errorlevel 1 (
        echo %RED%❌ 前端依赖安装失败%NC%
        pause
        exit /b 1
    )
)

start "若依前端服务" cmd /k "pnpm dev"

echo.
echo %GREEN%========================================%NC%
echo %GREEN%  演示环境启动完成！%NC%
echo %GREEN%========================================%NC%
echo.
echo %BLUE%访问地址：%NC%
echo   前端管理后台: http://localhost:3000
echo   后端API接口: http://localhost:%BACKEND_PORT%
echo   默认账号: admin / admin123
echo.
echo %YELLOW%注意事项：%NC%
echo   1. 首次启动可能需要等待1-2分钟
echo   2. 如需停止服务，请关闭对应的命令行窗口
echo   3. AI功能需要配置相应的API密钥
echo.

goto :eof

:: 处理命令行参数
if "%1"=="start-backend" goto :start_backend_only
if "%1"=="start-frontend" goto :start_frontend_only
if "%1"=="start-all" goto :start_all_services
if "%1"=="stop" goto :stop_services
if "%1"=="status" goto :check_status
if "%1"=="help" goto :show_help

goto :end

:start_backend_only
echo %YELLOW%正在启动后端服务...%NC%
cd /d "%BACKEND_DIR%"
if not exist "yudao-server\target\yudao-server.jar" (
    echo %RED%❌ 后端JAR文件不存在，请先运行完整部署%NC%
    pause
    exit /b 1
)
start "若依后端服务" cmd /k "java -jar yudao-server\target\yudao-server.jar --spring.profiles.active=local --server.port=%BACKEND_PORT%"
echo %GREEN%✅ 后端服务启动完成%NC%
echo %BLUE%访问地址: http://localhost:%BACKEND_PORT%%NC%
goto :end

:start_frontend_only
echo %YELLOW%正在启动前端服务...%NC%
cd /d "%FRONTEND_DIR%"
if not exist "node_modules" (
    echo %BLUE%正在安装前端依赖...%NC%
    call pnpm install
)
start "若依前端服务" cmd /k "pnpm dev"
echo %GREEN%✅ 前端服务启动完成%NC%
echo %BLUE%访问地址: http://localhost:3000%NC%
goto :end

:start_all_services
call :start_services
goto :end

:stop_services
echo %YELLOW%正在停止服务...%NC%
taskkill /f /im java.exe /fi "WINDOWTITLE eq 若依后端服务*" >nul 2>&1
taskkill /f /im node.exe /fi "WINDOWTITLE eq 若依前端服务*" >nul 2>&1
echo %GREEN%✅ 服务已停止%NC%
goto :end

:check_status
echo %BLUE%检查服务状态...%NC%
echo.
netstat -an | findstr ":%BACKEND_PORT%" >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ 后端服务未运行 (端口 %BACKEND_PORT%)%NC%
) else (
    echo %GREEN%✅ 后端服务正在运行 (端口 %BACKEND_PORT%)%NC%
)

netstat -an | findstr ":3000" >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ 前端服务未运行 (端口 3000)%NC%
) else (
    echo %GREEN%✅ 前端服务正在运行 (端口 3000)%NC%
)
goto :end

:show_help
echo.
echo %BLUE%若依Vue Pro + AI模块 部署脚本使用说明%NC%
echo.
echo %YELLOW%用法：%NC%
echo   %0                  - 完整部署流程
echo   %0 start-backend    - 仅启动后端服务
echo   %0 start-frontend   - 仅启动前端服务
echo   %0 start-all        - 启动所有服务
echo   %0 stop             - 停止所有服务
echo   %0 status           - 检查服务状态
echo   %0 help             - 显示此帮助信息
echo.
echo %YELLOW%环境要求：%NC%
echo   - JDK 17+
echo   - Maven 3.6+
echo   - Node.js 16+
echo   - MySQL 5.7+
echo   - Redis (可选)
echo.
goto :end

:end
if "%1"=="" pause
